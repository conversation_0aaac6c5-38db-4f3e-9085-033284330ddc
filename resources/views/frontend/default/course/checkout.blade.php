@extends('layouts.default')
@push('title',"Thanh toán ". $course_details->title)
@push('meta')@endpush
@push('css')
<link rel="stylesheet" href="{{ asset('assets/frontend/default/css/styles_shopee.css') }}"/>
<link
    rel="stylesheet"
    href="{{ asset('assets/frontend/default/libs/aos/aos-*******-beta.6.css') }}"
/>
<link rel="stylesheet" href="{{ asset('assets/css/checkout.css') }}"/>
<script src="{{ asset('assets/frontend/mst-academy/assets/libs/aos/aos-*******-beta.6.js') }}"></script>
@endpush



@section('content')

<div class="tax-landing main">
    <div class="tax-banner" style="background-image: url('{{ asset('assets/img/bg-checkout.png') }}');">
        <div class="container">
        <!-- Main Title -->
            <h1 class="main-title">
                ĐỪNG ĐỂ RỦI RO VỀ THUẾ!!!
            </h1>
            
            <!-- Subtitle -->
            <div class="subtitle">
                <strong style="color: #FBC30B;">1 Bước cuối</strong> cùng áp dụng hiệu quả các quy định về thuế, tối ưu hóa<br>
                nghĩa vụ thuế, giảm rủi ro pháp lý trong <strong>14 ngày!!!</strong><br>
                <strong>Bắt đầu ngay hôm nay!</strong>
            </div>
            

            <div class="d-flex justify-content-center arrow-container">
                <img src="{{ asset('assets/img/arrow-bot-animation.png') }}" alt="Arrow pointing down"
                
                width="42"
                height="50"
                class="w-auto h-9 md:h-10 lg:h-[50px] animate-bounce"
                data-aos-lazy="fade-down"
                data-aos-delay="300"
                data-aos-duration="1000"
                data-aos-repeat="true">
            </div>

            <!-- Promo Card -->
            <div class="promo-card">
                <div class="row align-items-center">
                        <img class="rocket-icon" src="{{ asset('assets/img/rocket-icon.png') }}" alt="Rocket Icon" width="80" height="80">
                    <div class="col">
                        <h3>200 doanh nghiệp dùng thuế không sai phạm</h3>
                        <p>
                            Là con số mà TopID đã hỗ trợ trực tiếp và bây giờ bạn hoàn toàn làm được theo quy 
                            trình mà TopID đã soạn sẵn!!!
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Slots Section -->
            <div class="slots-section">
                <h2 class="slots-title">
                    <span style="color: #FBC30B;">7 SLOT</span> CUỐI CÙNG
                </h2>
                <p class="slots-subtitle">
                    Nhận ngay ưu đãi hấp dẫn khi mua khóa học tối ưu<br>
                    hóa nghĩa vụ thuế, giảm rủi ro pháp lý!
                </p>

                <!-- Sparkle Effects -->
                <div class="absolute inset-0 z-0">
                    <div class="sparkle" style="width: 12px; height: 12px; top: -15%; left: 10%; animation-delay: 0s;"></div>
                    <div class="sparkle" style="width: 8px; height: 8px; top: 10%; left: 95%; animation-delay: 0.2s;"></div>
                    <div class="sparkle" style="width: 10px; height: 10px; top: 80%; left: 105%; animation-delay: 0.4s;"></div>
                    <div class="sparkle" style="width: 14px; height: 14px; top: 105%; left: 60%; animation-delay: 0.6s;"></div>
                    <div class="sparkle" style="width: 9px; height: 9px; top: 60%; left: -5%; animation-delay: 0.8s;"></div>
                    <div class="sparkle" style="width: 11px; height: 11px; top: 30%; left: -10%; animation-delay: 1s;"></div>
                    <div class="sparkle" style="width: 7px; height: 7px; top: 90%; left: 25%; animation-delay: 1.2s;"></div>
                </div>
                <div class="progress-container flex items-center justify-center max-w-2xl mx-auto">
                    
                    <img 
                            src="{{ asset('assets/img/gift.png') }}" 
                            alt="Biểu tượng hộp quà" 
                            class="gift-icon"
                        >

                    <div class="progress progress-bar-custom" role="progressbar" aria-label="Default striped example" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100">
                        <div class="progress-bar progress-bar-striped" style="width: 30%"></div>
                    </div>

                </div>
            
                
                <!-- Dots Pattern -->
                <div class="story__dots animation-dots">
                    <img src="{{ asset('assets/img/section-below.png') }}" alt="">
                </div>
            </div>
        </div>
    </div>

    <div class="section-buy" style="background-image: url('{{ asset('assets/img/bg-buynow.png') }}');">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <div class="buy-content">
                        <img src="{{ asset('assets/img/special-tag.png') }}" alt="" class="special-tag">

                        <h5 class="buy-title">ƯU ĐÃI KHI MUA KHÓA HỌC</h5>
                        
                        <div class="benefits-list">
                            <div class="benefit-item">
                                <div class="benefit-icon">✓</div>
                                <div class="d-flex flex-column">
                                    <span class="b-box">Được tham gia free</span>
                                    <span class="highlight-text">Tất cả các buổi workshop và offline định kỳ</span>
                                </div>
                            </div>
                            <div class="benefit-item">
                                <div class="benefit-icon">✓</div>
                                <div class="d-flex flex-column">
                                    <span class="b-box">Free Mentor Marketing</span>
                                    <span class="highlight-text">Marketing Manager ở TopID</span>
                                </div>
                            </div>
                            <div class="benefit-item">
                                <div class="benefit-icon">✓</div>
                                <div class="d-flex flex-column">
                                    <span class="b-box">Hỗ trợ bạn xây dựng khóa học</span>
                                    <span class="highlight-text">Xây dựng khóa học bài bản cùng đội ngũ support liên tục, đẩy khóa học ra mắt nhanh (case nhanh nhất 5 ngày)</span>
                                </div>
                            </div>
                            <div class="benefit-item">
                                <div class="benefit-icon">✓</div>
                                <div class="d-flex flex-column">
                                    <span class="b-box">Trợ giá 15%</span>
                                    <span class="highlight-text">Các dịch vụ hình ảnh và video</span>
                                </div>
                            </div>
                            <div class="benefit-item">
                                <div class="benefit-icon">✓</div>
                                <div class="d-flex flex-column">
                                    <span class="b-box">Trợ giá 10%</span>
                                    <span class="highlight-text">Các dịch vụ video ads và xây kênh</span>
                                </div>
                            </div>
                        </div>
                        
                    </div>
                    
                    <div class="course-preview">
                        <div class="course-image-container">
                                <img src="https://elearning.topid.vn/uploads/course-thumbnail/-1744433510.png"  class="course-image">

                            
                            <div class="play-overlay" onclick="playVideo()">
                                <div class="play-button">
                                    <i class="fas fa-play"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6 modal-regis">
                    <div class="regis-content">

                    <div class="hot-deal">
                        <div class="hot-deal__original-price">4.000.000đ</div>
                        <div class="hot-deal__promo">
                            <span class="hot-deal__promo-label">Chỉ còn:</span>
                            <strong class="hot-deal__promo-price">2.800.000đ</strong>
                            <img class="hot-deal__promo-icon" src="{{ asset('assets/img/hot-deal.svg') }}" alt="Hot Deal Icon">
                        </div>
                    </div>

                        <div class="price-info">
                            <span>Gói đã chọn:</span>
                            <span id="selected-plan-name">Trọn đời</span>
                        </div>
                        <div class="price-info">
                            <span>Giá gốc:</span>
                            <span class="text-decoration-line-through" id="original-price-display">2.990.000&nbsp;₫</span>
                        </div>
                        <div class="price-info">
                            <span>Giá ưu đãi hôm nay:</span>
                            <strong id="current-price-display">2.990.000&nbsp;₫</strong>
                        </div>
                        <div class="input-group mt-3" id="coupon_form">
                            <input type="text" class="form-control" id="couponInput" placeholder="Mã ưu đãi">
                            <button class="btn code-btn text-white" type="button" id="applyCoupon">
                                Áp dụng
                            </button>
                        </div>
                        <div class="total-price">
                            <span class="fw-bold">Tổng thanh toán:</span>
                            <span class="price" id="total_amount">2.990.000&nbsp;₫</span>
                        </div>

                        <div class="user-info-container">
                            <h6 class="fw-bold mb-3">Thông tin của bạn:</h6>
                            <p class="mb-4 change-login-register">
                                Bạn đã có tài khoản?
                                <a class="login-link">Ấn vào đây
                                    để đăng nhập</a>
                            </p>

                            <div class="mb-4 form-wrap">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" value="" class="form-control" name="email" id="payment_register_email" required="">
                                                                        </div>

                            <div class="mb-4 form-wrap">
                                <label for="password" class="form-label">Mật khẩu</label>
                                <div class="password-field">
                                    <input type="password" class="form-control" name="password" id="payment_register_password" required="">
                                    <button class="password-toggle" type="button">
                                        <i class="bi bi-eye-slash"></i>
                                    </button>
                                </div>
                                                                        </div>

                            <div class="mb-4 form-wrap is-show">
                                <label for="phone" class="form-label">Số điện thoại</label>
                                <div class="input-group phone-group">
                                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <img src="https://elearning.topid.vn/assets/frontend/course-shopee/assets/images/vn.png" alt="Vietnam Flag" width="20" height="15">
                                        +84
                                    </button>
                                    <input type="tel" value="" class="form-control" name="phone" id="payment_register_phone">

                                </div>
                                                                        </div>

                            <p class="small text-muted mb-3 is-show">
                                Thông tin cá nhân của bạn sẽ được sử dụng để xử lý đơn hàng,
                                tăng trải nghiệm sử dụng website, và cho các mục đích cụ thể
                                khác đã được mô tả trong
                                <a href="#" class="privacy-link">chính sách riêng tư</a>
                                của chúng tôi.
                            </p>
                            <div class="is-show">
                                <div class="form-check mb-3 d-flex">
                                    <input class="form-check-input" name="terms" type="checkbox" id="payment_register_terms">
                                    <label class="form-check-label" for="payment_register_terms" style="font-size: 14px;">
                                        Tôi đã đọc và đồng ý với
                                        <a href="#" class="privacy-link">điều khoản và điều kiện</a>
                                        của website
                                    </label>
                                </div>
                                                                        </div>

                            <div><div class="grecaptcha-badge" data-style="bottomright" style="width: 256px; height: 60px; display: block; transition: right 0.3s; position: fixed; bottom: 14px; right: -186px; box-shadow: gray 0px 0px 5px; border-radius: 2px; overflow: hidden;"><div class="grecaptcha-logo"><iframe title="reCAPTCHA" width="256" height="60" role="presentation" name="a-jp1jqozf32rh" frameborder="0" scrolling="no" sandbox="allow-forms allow-popups allow-same-origin allow-scripts allow-top-navigation allow-modals allow-popups-to-escape-sandbox allow-storage-access-by-user-activation" src="https://www.google.com/recaptcha/api2/anchor?ar=1&amp;k=6LeB1BUrAAAAADVvLh3_4bp6OoqRDBkTT1gh0G-q&amp;co=aHR0cHM6Ly9lbGVhcm5pbmcudG9waWQudm46NDQz&amp;hl=en&amp;v=h7qt2xUGz2zqKEhSc8DD8baZ&amp;size=invisible&amp;sa=submit&amp;cb=v2exle5xpjl7"></iframe></div><div class="grecaptcha-error"></div><textarea id="g-recaptcha-response" name="g-recaptcha-response" class="g-recaptcha-response" style="width: 250px; height: 40px; border: 1px solid rgb(193, 193, 193); margin: 10px 25px; padding: 0px; resize: none; display: none;"></textarea></div></div><button class="btn g-recaptcha g-recaptcha-checkout" id="btn-checkout-regis" data-sitekey="6LeB1BUrAAAAADVvLh3_4bp6OoqRDBkTT1gh0G-q" data-callback="onHandlecheckoutPayment" data-action="submit" type="button">
                                Mua ngay
                            </button>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="section-solution solution" style="background-image: url('{{ asset('assets/img/bg-solution.png') }}');">
        <div class="text-center course-detail-title">Chi tiết khóa học</div>
        <div class="course-detail-desc">Khóa học gồm <div class="module-count">5 modules</div> với nội dung cực kỳ thực tế.</div>
        <div class="container">
            <div class="shopee-course-interface">
                <div class="d-flex course-container">

                    @if ($sections->count() > 0)
                        <div class="course-list w-full" style="width: 100% !important;">
                            <div
                                class="accordion modules-list accordion-flush"
                                id="course-modules-accordion"
                            >
                                @foreach ($sections as $key => $section)
                                    @php
                                        $lessons = App\Models\Lesson::where('section_id', $section->id)
                                            ->orderBy('sort')
                                            ->get();

                                        // Calculate total duration and video count for this section
                                        $total_duration = 0;
                                        $video_count = 0;
                                        foreach ($lessons as $lesson) {
                                            $duration = lesson_durations($lesson->id);
                                            if ($duration != '00:00:00') {
                                                $time_parts = explode(':', $duration);
                                                if (count($time_parts) == 3) {
                                                    $total_duration += ($time_parts[0] * 3600) + ($time_parts[1] * 60) + $time_parts[2];
                                                }
                                            }

                                            if (in_array($lesson->lesson_type, ['video-url', 'system-video', 'vimeo-url', 'google_drive','iframe'])) {
                                                $video_count++;
                                            }
                                        }

                                        $hours = floor($total_duration / 3600);
                                        $minutes = floor(($total_duration % 3600) / 60);
                                        $seconds = $total_duration % 60;
                                        $formatted_duration = sprintf("%02d:%02d", $hours, $minutes);
                                    @endphp
                                    <div class="module-item accordion-item">
                                        <div
                                            class="module-header accordion-header d-flex justify-content-between align-items-center"
                                        >
                                            <div
                                                data-bs-toggle="collapse"
                                                data-bs-target="#course-module-content-{{ $section->id }}"
                                                aria-expanded="{{ $key == 0 ? 'true' : 'false' }}"
                                                aria-controls="course-module-content-{{ $section->id }}"
                                                class="{{ $key == 0 ? 'a' : 'collapsed' }}"
                                            >
                                                <div class="module-title">
                                                    <h3 class="text-h6 mb-0">
                                                        {{ ucfirst($section->title) }}
                                                    </h3>
                                                    <div class="module-details">
                                                <span class="video-count text-text14">
                                                    <img
                                                        src="{{ asset('assets/frontend/default/images_shopee/video-icon.svg')}}"
                                                        alt="Video"
                                                    />
                                                    <span>
                                                        Số lượng video:
                                                        <strong>{{ $video_count }}</strong>
                                                    </span>
                                                </span>
                                                        <span class="duration text-text14">
                                                    <img
                                                        src="{{ asset('assets/frontend/default/images_shopee/clock-icon.svg')}}"
                                                        alt="Clock"
                                                    />
                                                    <span>
                                                        Thời lượng:
                                                        <strong>{{ $formatted_duration }}</strong>
                                                    </span>
                                                </span>
                                                    </div>
                                                </div>
                                                <div class="toggle-btn">
                                                    <img
                                                        class="toggle-btn-icon toggle-btn-icon-collapse"
                                                        src="{{ asset('assets/frontend/default/images_shopee/minus.svg')}}"
                                                        alt="Collapse"
                                                    />
                                                    <img
                                                        class="toggle-btn-icon toggle-btn-icon-expand"
                                                        src="{{ asset('assets/frontend/default/images_shopee/plus.svg')}}"
                                                        alt="Expand"
                                                    />
                                                </div>
                                            </div>
                                        </div>

                                        <div
                                            class="module-content accordion-collapse collapse @if($key == 0) show @endif"
                                            id="course-module-content-{{ $section->id }}"
                                            data-bs-parent="#course-modules-accordion"
                                        >
                                            <div class="accordion-body">
                                                {{--                                                        <p class="module-content-description">--}}
                                                {{--                                                            Cách mở gian hàng, cách xác định sản phẩm--}}
                                                {{--                                                            tiềm năng để bắt đầu bán hàng trên Shopee.--}}
                                                {{--                                                        </p>--}}

                                                @foreach ($lessons as $lesson)
                                                    @php
                                                        $duration = lesson_durations($lesson->id);
                                                    @endphp
                                                    <div class="lesson-item">
                                                        <a href="{{ route('course.player', ['slug' => $course_details->slug, 'id' => $lesson->id]) }}"
                                                            class="lesson-link"
                                                        >
                                                            <p
                                                                class="text-text14 lesson-item-title"
                                                            >
                                                                {{ ucfirst($lesson->title) }}
                                                            </p>
                                                            <div class="d-flex align-items-center">
                                                                @if($lesson->paid_lesson)
                                                                    <span class="badge premium-trial-badge">
                                                                        <img
                                                                            src="{{ asset('assets/frontend/default/images_shopee/crown-icon.svg')}}"
                                                                            width="12.941px"
                                                                            height="12.941px"
                                                                            alt="Coin"
                                                                        />
                                                                        <span>PRO</span>
                                                                    </span>
                                                                @elseif($lesson->trial_lesson)
                                                                    <span class="badge pro-trial-badge">
                                                                        <img
                                                                            src="{{ asset('assets/frontend/default/images_shopee/crown-icon.svg')}}"
                                                                            width="12.941px"
                                                                            height="12.941px"
                                                                            alt="Coin"
                                                                        />
                                                                        <span>{{get_phrase('PRO TRIAL')}}</span>
                                                                    </span>
                                                                @else
                                                                    <span class="badge free-badge">
                                                                        <img
                                                                            src="{{ asset('assets/frontend/default/images_shopee/coin.svg')}}"
                                                                            width="11"
                                                                            height="11"
                                                                            alt="Coin"
                                                                        />
                                                                        <span>{{get_phrase('FREE')}}</span>
                                                                    </span>
                                                                @endif
                                                                @if($lesson->is_important)
                                                                    <span class="badge important-badge">
                                                                        <img
                                                                            src="{{ asset('assets/frontend/default/images_shopee/important.svg')}}"
                                                                            width="11"
                                                                            height="11"
                                                                            alt="IMPORTANT"
                                                                        />
                                                                        <span>{{get_phrase('IMPORTANT')}}</span>
                                                                    </span>
                                                                @endif
                                                                @if($duration != '00:00:00')
                                                                    <span class="duration-small">
                                                                        <img
                                                                            src="{{ asset('assets/frontend/default/images_shopee/clock.svg')}}"
                                                                            alt="Clock"
                                                                        />
                                                                        Thời lượng: <strong>{{ $duration }}</strong>
                                                                    </span>
                                                                @endif
                                                            </div>
                                                        </a>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                @endforeach

                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <img src="{{ asset('assets/img/bottom-banner.png') }}" alt="" class="bottom-banner" style="width: 100%;">
</div>


@endsection

@push('js')
<script>
document.addEventListener("DOMContentLoaded", function () {
	// Password toggle functionality
	const toggleButtons = document.querySelectorAll(".password-toggle");

	toggleButtons.forEach((button) => {
		button.addEventListener("click", function () {
			const input = this.previousElementSibling;
			const icon = this.querySelector("i");

			if (input.type === "password") {
				input.type = "text";
				icon.classList.replace("bi-eye-slash", "bi-eye");
			} else {
				input.type = "password";
				icon.classList.replace("bi-eye", "bi-eye-slash");
			}
		});
	});
});

</script>

<script>
function playVideo() {
        // If there's a preview video, open it in a modal or redirect to video player
        var videoUrl = "https://www.w3schools.com/html/mov_bbb.mp4";
        
        // Create a modal to show the video
        var modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        
        var videoContainer = document.createElement('div');
        videoContainer.style.cssText = `
            position: relative;
            width: 90%;
            max-width: 800px;
            aspect-ratio: 16/9;
        `;
        
        var closeBtn = document.createElement('button');
        closeBtn.innerHTML = '×';
        closeBtn.style.cssText = `
            position: absolute;
            top: -40px;
            right: 0;
            background: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            z-index: 10000;
        `;
        closeBtn.onclick = function() {
            document.body.removeChild(modal);
        };
        
        var video = document.createElement('video');
        video.style.cssText = `
            width: 100%;
            height: 100%;
            object-fit: cover;
        `;
        video.controls = true;
        video.autoplay = true;
        video.src = videoUrl;
        
        videoContainer.appendChild(closeBtn);
        videoContainer.appendChild(video);
        modal.appendChild(videoContainer);
        document.body.appendChild(modal);
        
        // Close modal when clicking outside
        modal.onclick = function(e) {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        };
}

// Initialize AOS animations
AOS.init({
    duration: 1000,
    once: true,
    offset: 100
});
</script>
@endpush
